/**
 * Data models and types for the Ipon Challenge app
 */

// Savings entry types
export const SAVINGS_TYPES = {
  BOX: 'box',
  ATM: 'atm',
  BANK: 'bank',
  DIGITAL: 'digital'
};

// Savings entry type labels
export const SAVINGS_TYPE_LABELS = {
  [SAVINGS_TYPES.BOX]: 'Piggy Bank/Box',
  [SAVINGS_TYPES.ATM]: 'ATM Deposit',
  [SAVINGS_TYPES.BANK]: 'Bank Counter',
  [SAVINGS_TYPES.DIGITAL]: 'Digital Wallet'
};

/**
 * Creates a new savings entry object
 * @param {Object} data - The savings entry data
 * @param {number} data.amount - The amount saved
 * @param {string} data.type - The type of savings (box, atm, bank, digital)
 * @param {string} data.notes - Optional notes about the savings
 * @param {Date} data.date - The date of the savings (defaults to now)
 * @returns {Object} A new savings entry object
 */
export function createSavingsEntry({ amount, type, notes = '', date = new Date() }) {
  return {
    id: generateId(),
    amount: parseFloat(amount),
    type,
    notes: notes.trim(),
    date: date instanceof Date ? date : new Date(date),
    createdAt: new Date()
  };
}

/**
 * Validates a savings entry object
 * @param {Object} entry - The savings entry to validate
 * @returns {Object} Validation result with isValid boolean and errors array
 */
export function validateSavingsEntry(entry) {
  const errors = [];

  if (!entry.amount || entry.amount <= 0) {
    errors.push('Amount must be greater than 0');
  }

  if (!entry.type || !Object.values(SAVINGS_TYPES).includes(entry.type)) {
    errors.push('Please select a valid savings type');
  }

  if (!entry.date || isNaN(new Date(entry.date).getTime())) {
    errors.push('Please provide a valid date');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Generates a unique ID for savings entries
 * @returns {string} A unique identifier
 */
function generateId() {
  return Date.now().toString(36) + Math.random().toString(36).substr(2);
}

/**
 * Formats currency amount for display
 * @param {number} amount - The amount to format
 * @param {string} currency - The currency symbol (default: ₱)
 * @returns {string} Formatted currency string
 */
export function formatCurrency(amount, currency = '₱') {
  return `${currency}${amount.toLocaleString('en-US', { 
    minimumFractionDigits: 2, 
    maximumFractionDigits: 2 
  })}`;
}

/**
 * Calculates total savings from an array of entries
 * @param {Array} entries - Array of savings entries
 * @returns {number} Total amount saved
 */
export function calculateTotalSavings(entries) {
  return entries.reduce((total, entry) => total + entry.amount, 0);
}

/**
 * Groups savings entries by type
 * @param {Array} entries - Array of savings entries
 * @returns {Object} Object with entries grouped by type
 */
export function groupEntriesByType(entries) {
  return entries.reduce((groups, entry) => {
    const type = entry.type;
    if (!groups[type]) {
      groups[type] = [];
    }
    groups[type].push(entry);
    return groups;
  }, {});
}

/**
 * Gets savings statistics
 * @param {Array} entries - Array of savings entries
 * @returns {Object} Statistics object
 */
export function getSavingsStatistics(entries) {
  if (entries.length === 0) {
    return {
      total: 0,
      count: 0,
      average: 0,
      byType: {},
      lastEntry: null
    };
  }

  const total = calculateTotalSavings(entries);
  const byType = groupEntriesByType(entries);
  const typeStats = {};

  Object.keys(byType).forEach(type => {
    typeStats[type] = {
      count: byType[type].length,
      total: calculateTotalSavings(byType[type])
    };
  });

  return {
    total,
    count: entries.length,
    average: total / entries.length,
    byType: typeStats,
    lastEntry: entries[entries.length - 1]
  };
}
