/* Dashboard Component Styles */
.dashboard {
  padding: 0;
}

.welcome-section {
  background: rgba(255, 255, 255, 0.9);
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  margin-bottom: 2rem;
  text-align: center;
}

.welcome-section h2 {
  margin: 0 0 0.5rem 0;
  color: #2d3748;
  font-size: 1.75rem;
}

.welcome-section p {
  margin: 0;
  color: #4a5568;
  font-size: 1.1rem;
}

/* Stats Grid */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.stat-card {
  background: rgba(255, 255, 255, 0.9);
  padding: 1.5rem;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 1rem;
  transition: transform 0.2s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
}

.stat-card.primary {
  background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
  color: white;
}

.stat-card.secondary {
  background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
  color: white;
}

.stat-card.tertiary {
  background: linear-gradient(135deg, #ed8936 0%, #dd6b20 100%);
  color: white;
}

.stat-card.goal {
  background: linear-gradient(135deg, #9f7aea 0%, #805ad5 100%);
  color: white;
}

.stat-icon {
  font-size: 2rem;
  opacity: 0.9;
}

.stat-content h3 {
  margin: 0 0 0.5rem 0;
  font-size: 0.9rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  opacity: 0.9;
}

.stat-value {
  margin: 0;
  font-size: 1.75rem;
  font-weight: bold;
}

.stat-subtitle {
  margin: 0;
  font-size: 0.8rem;
  opacity: 0.8;
}

.progress-bar {
  width: 100%;
  height: 4px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 2px;
  margin-top: 0.5rem;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 2px;
  transition: width 0.3s ease;
}

/* Goal Section */
.goal-section {
  background: rgba(255, 255, 255, 0.9);
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  margin-bottom: 2rem;
}

.goal-section h3 {
  margin: 0 0 1.5rem 0;
  color: #2d3748;
  text-align: center;
}

.goal-amounts {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 1rem;
}

.current-amount {
  font-size: 1.5rem;
  font-weight: bold;
  color: #4299e1;
}

.goal-separator {
  color: #718096;
  font-size: 1rem;
}

.goal-amount {
  font-size: 1.5rem;
  font-weight: bold;
  color: #2d3748;
}

.goal-progress-bar {
  width: 100%;
  height: 8px;
  background: #e2e8f0;
  border-radius: 4px;
  margin-bottom: 0.5rem;
  overflow: hidden;
}

.goal-progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #4299e1, #3182ce);
  border-radius: 4px;
  transition: width 0.3s ease;
}

.goal-remaining {
  text-align: center;
  color: #4a5568;
  font-size: 0.9rem;
}

.goal-achieved {
  color: #48bb78;
  font-weight: bold;
}

/* Recent Activity */
.recent-activity {
  background: rgba(255, 255, 255, 0.9);
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  margin-bottom: 2rem;
}

.recent-activity h3 {
  margin: 0 0 1rem 0;
  color: #2d3748;
}

.activity-card {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: #f7fafc;
  border-radius: 8px;
  border-left: 4px solid #4299e1;
}

.activity-amount {
  font-size: 1.25rem;
  font-weight: bold;
  color: #4299e1;
}

.activity-details {
  flex: 1;
}

.activity-type {
  margin: 0 0 0.25rem 0;
  font-size: 0.8rem;
  font-weight: 600;
  color: #718096;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.activity-date {
  margin: 0 0 0.25rem 0;
  font-size: 0.9rem;
  color: #4a5568;
}

.activity-notes {
  margin: 0;
  font-size: 0.9rem;
  color: #718096;
  font-style: italic;
}

/* Motivation Section */
.motivation-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  color: white;
  text-align: center;
}

.motivation-card h3 {
  margin: 0 0 1rem 0;
  font-size: 1.5rem;
}

.motivation-card p {
  margin: 0;
  font-size: 1.1rem;
  opacity: 0.9;
  line-height: 1.6;
}

/* Responsive Design */
@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .stat-card {
    padding: 1.25rem;
  }
  
  .stat-value {
    font-size: 1.5rem;
  }
  
  .goal-amounts {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .current-amount,
  .goal-amount {
    font-size: 1.25rem;
  }
  
  .activity-card {
    flex-direction: column;
    text-align: center;
    gap: 0.75rem;
  }
}

@media (max-width: 480px) {
  .welcome-section,
  .goal-section,
  .recent-activity,
  .motivation-section {
    padding: 1.5rem;
  }
  
  .welcome-section h2 {
    font-size: 1.5rem;
  }
  
  .stat-card {
    padding: 1rem;
  }
  
  .stat-icon {
    font-size: 1.5rem;
  }
  
  .stat-value {
    font-size: 1.25rem;
  }
}
