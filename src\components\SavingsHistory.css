/* Savings History Component Styles */
.savings-history {
  max-width: 800px;
  margin: 0 auto;
}

/* Header */
.history-header {
  background: rgba(255, 255, 255, 0.9);
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  margin-bottom: 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.history-header h2 {
  margin: 0;
  color: #2d3748;
  font-size: 1.75rem;
}

.history-stats {
  display: flex;
  gap: 2rem;
}

.stat {
  color: #4a5568;
  font-size: 0.9rem;
}

.stat strong {
  color: #2d3748;
  font-size: 1.1rem;
}

/* Controls */
.history-controls {
  background: rgba(255, 255, 255, 0.9);
  padding: 1.5rem;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  margin-bottom: 1rem;
  display: flex;
  gap: 1rem;
  align-items: center;
  flex-wrap: wrap;
}

.search-box {
  position: relative;
  flex: 1;
  min-width: 200px;
}

.search-icon {
  position: absolute;
  left: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  color: #718096;
}

.search-input {
  width: 100%;
  padding: 0.75rem 0.75rem 0.75rem 2.5rem;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  font-size: 0.9rem;
  transition: border-color 0.2s ease;
}

.search-input:focus {
  outline: none;
  border-color: #4299e1;
}

.filter-group,
.sort-group {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.filter-icon {
  color: #718096;
}

.filter-select,
.sort-select {
  padding: 0.75rem;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  font-size: 0.9rem;
  background: white;
  cursor: pointer;
  transition: border-color 0.2s ease;
}

.filter-select:focus,
.sort-select:focus {
  outline: none;
  border-color: #4299e1;
}

/* Results Info */
.results-info {
  padding: 0.5rem 1rem;
  background: rgba(66, 153, 225, 0.1);
  border-radius: 8px;
  color: #2b6cb0;
  font-size: 0.9rem;
  margin-bottom: 1rem;
}

/* History List */
.history-list {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.history-group {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.group-header {
  background: #f7fafc;
  padding: 1rem 1.5rem;
  margin: 0;
  color: #2d3748;
  font-size: 1.1rem;
  font-weight: 600;
  border-bottom: 1px solid #e2e8f0;
}

.group-entries {
  padding: 0;
}

/* History Entry */
.history-entry {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem;
  border-bottom: 1px solid #f1f5f9;
  transition: background-color 0.2s ease;
}

.history-entry:last-child {
  border-bottom: none;
}

.history-entry:hover {
  background: #f8fafc;
}

.entry-icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.25rem;
  flex-shrink: 0;
}

.entry-content {
  flex: 1;
}

.entry-main {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.entry-amount {
  font-size: 1.25rem;
  font-weight: bold;
  color: #2d3748;
}

.entry-type {
  font-size: 0.8rem;
  font-weight: 600;
  color: #718096;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.entry-details {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.entry-date {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.85rem;
  color: #4a5568;
}

.date-icon {
  font-size: 0.75rem;
}

.entry-notes {
  font-size: 0.85rem;
  color: #718096;
  font-style: italic;
}

/* Entry Actions */
.entry-actions {
  display: flex;
  gap: 0.5rem;
}

.action-btn {
  width: 36px;
  height: 36px;
  border: none;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.9rem;
}

.edit-btn {
  background: #e2e8f0;
  color: #4a5568;
}

.edit-btn:hover {
  background: #cbd5e0;
  color: #2d3748;
}

.delete-btn {
  background: #fed7d7;
  color: #c53030;
}

.delete-btn:hover {
  background: #feb2b2;
  color: #9b2c2c;
}

/* Empty State */
.history-empty {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

.empty-state {
  text-align: center;
  color: #4a5568;
}

.empty-icon {
  font-size: 4rem;
  color: #cbd5e0;
  margin-bottom: 1rem;
}

.empty-state h3 {
  margin: 0 0 0.5rem 0;
  font-size: 1.5rem;
  color: #2d3748;
}

.empty-state p {
  margin: 0;
  font-size: 1.1rem;
}

/* No Results */
.no-results {
  text-align: center;
  padding: 3rem;
  color: #4a5568;
}

/* Responsive Design */
@media (max-width: 768px) {
  .history-header {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }
  
  .history-stats {
    gap: 1rem;
  }
  
  .history-controls {
    flex-direction: column;
    align-items: stretch;
  }
  
  .search-box {
    min-width: auto;
  }
  
  .filter-group,
  .sort-group {
    justify-content: center;
  }
  
  .history-entry {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }
  
  .entry-main {
    width: 100%;
  }
  
  .entry-actions {
    align-self: flex-end;
  }
}

@media (max-width: 480px) {
  .savings-history {
    margin: 0 -0.5rem;
  }
  
  .history-header,
  .history-controls,
  .history-group {
    margin-left: 0.5rem;
    margin-right: 0.5rem;
  }
  
  .history-entry {
    padding: 1rem;
  }
  
  .entry-icon {
    width: 40px;
    height: 40px;
    font-size: 1rem;
  }
  
  .entry-amount {
    font-size: 1.1rem;
  }
}
