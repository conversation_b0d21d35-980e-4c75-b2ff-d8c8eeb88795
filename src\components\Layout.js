import React from 'react';
import { FaPiggyBank, FaCog, FaHome, FaPlus, FaHistory } from 'react-icons/fa';
import './Layout.css';

/**
 * Main layout component with navigation
 */
function Layout({ children, currentView, onViewChange, darkMode }) {
  const navItems = [
    { id: 'dashboard', label: 'Dashboard', icon: FaHome },
    { id: 'add', label: 'Add Savings', icon: FaPlus },
    { id: 'history', label: 'History', icon: FaHistory },
    { id: 'settings', label: 'Settings', icon: FaCog }
  ];

  return (
    <div className={`layout ${darkMode ? 'dark-mode' : ''}`}>
      {/* Header */}
      <header className="layout-header">
        <div className="header-content">
          <div className="logo">
            <FaPiggyBank className="logo-icon" />
            <h1>Ipon Challenge</h1>
          </div>
          <p className="tagline">Build your savings habit, one coin at a time!</p>
        </div>
      </header>

      {/* Main Content */}
      <main className="layout-main">
        <div className="main-content">
          {children}
        </div>
      </main>

      {/* Bottom Navigation */}
      <nav className="bottom-nav">
        {navItems.map(item => {
          const Icon = item.icon;
          return (
            <button
              key={item.id}
              className={`nav-item ${currentView === item.id ? 'active' : ''}`}
              onClick={() => onViewChange(item.id)}
              aria-label={item.label}
            >
              <Icon className="nav-icon" />
              <span className="nav-label">{item.label}</span>
            </button>
          );
        })}
      </nav>
    </div>
  );
}

export default Layout;
