import React, { useState } from 'react';
import { FaPiggyBank, FaUniversity, FaCreditCard, FaMobile, FaTrash, FaEdit, FaCalendarAlt, FaFilter, FaSearch } from 'react-icons/fa';
import { formatCurrency, SAVINGS_TYPES, SAVINGS_TYPE_LABELS } from '../utils/dataModels';
import { format, isToday, isYesterday, isThisWeek, isThisMonth } from 'date-fns';
import './SavingsHistory.css';

/**
 * Component for displaying savings history with filtering and search
 */
function SavingsHistory({ entries, statistics, currency, onDeleteEntry, onEditEntry }) {
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState('all');
  const [sortBy, setSortBy] = useState('date-desc');

  const typeIcons = {
    [SAVINGS_TYPES.BOX]: FaPiggyBank,
    [SAVINGS_TYPES.ATM]: FaCreditCard,
    [SAVINGS_TYPES.BANK]: FaUniversity,
    [SAVINGS_TYPES.DIGITAL]: FaMobile
  };

  // Filter and search entries
  const filteredEntries = entries.filter(entry => {
    const matchesSearch = entry.notes.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         entry.amount.toString().includes(searchTerm) ||
                         SAVINGS_TYPE_LABELS[entry.type].toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesFilter = filterType === 'all' || entry.type === filterType;
    
    return matchesSearch && matchesFilter;
  });

  // Sort entries
  const sortedEntries = [...filteredEntries].sort((a, b) => {
    switch (sortBy) {
      case 'date-desc':
        return new Date(b.date) - new Date(a.date);
      case 'date-asc':
        return new Date(a.date) - new Date(b.date);
      case 'amount-desc':
        return b.amount - a.amount;
      case 'amount-asc':
        return a.amount - b.amount;
      default:
        return new Date(b.date) - new Date(a.date);
    }
  });

  // Group entries by date periods
  const groupedEntries = sortedEntries.reduce((groups, entry) => {
    const entryDate = new Date(entry.date);
    let groupKey;

    if (isToday(entryDate)) {
      groupKey = 'Today';
    } else if (isYesterday(entryDate)) {
      groupKey = 'Yesterday';
    } else if (isThisWeek(entryDate)) {
      groupKey = 'This Week';
    } else if (isThisMonth(entryDate)) {
      groupKey = 'This Month';
    } else {
      groupKey = format(entryDate, 'MMMM yyyy');
    }

    if (!groups[groupKey]) {
      groups[groupKey] = [];
    }
    groups[groupKey].push(entry);
    return groups;
  }, {});

  const handleDelete = async (entryId) => {
    if (window.confirm('Are you sure you want to delete this savings entry?')) {
      await onDeleteEntry(entryId);
    }
  };

  if (entries.length === 0) {
    return (
      <div className="history-empty">
        <div className="empty-state">
          <FaPiggyBank className="empty-icon" />
          <h3>No Savings Yet</h3>
          <p>Start your savings journey by adding your first entry!</p>
        </div>
      </div>
    );
  }

  return (
    <div className="savings-history">
      {/* Header */}
      <div className="history-header">
        <h2>Savings History</h2>
        <div className="history-stats">
          <span className="stat">
            <strong>{statistics.count}</strong> entries
          </span>
          <span className="stat">
            <strong>{formatCurrency(statistics.total, currency)}</strong> total
          </span>
        </div>
      </div>

      {/* Controls */}
      <div className="history-controls">
        {/* Search */}
        <div className="search-box">
          <FaSearch className="search-icon" />
          <input
            type="text"
            placeholder="Search entries..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="search-input"
          />
        </div>

        {/* Filter */}
        <div className="filter-group">
          <FaFilter className="filter-icon" />
          <select
            value={filterType}
            onChange={(e) => setFilterType(e.target.value)}
            className="filter-select"
          >
            <option value="all">All Types</option>
            {Object.entries(SAVINGS_TYPE_LABELS).map(([type, label]) => (
              <option key={type} value={type}>{label}</option>
            ))}
          </select>
        </div>

        {/* Sort */}
        <div className="sort-group">
          <select
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value)}
            className="sort-select"
          >
            <option value="date-desc">Newest First</option>
            <option value="date-asc">Oldest First</option>
            <option value="amount-desc">Highest Amount</option>
            <option value="amount-asc">Lowest Amount</option>
          </select>
        </div>
      </div>

      {/* Results count */}
      {searchTerm || filterType !== 'all' ? (
        <div className="results-info">
          Showing {sortedEntries.length} of {entries.length} entries
        </div>
      ) : null}

      {/* Entries List */}
      <div className="history-list">
        {Object.entries(groupedEntries).map(([groupName, groupEntries]) => (
          <div key={groupName} className="history-group">
            <h3 className="group-header">{groupName}</h3>
            <div className="group-entries">
              {groupEntries.map(entry => {
                const Icon = typeIcons[entry.type];
                return (
                  <div key={entry.id} className="history-entry">
                    <div className="entry-icon">
                      <Icon />
                    </div>
                    <div className="entry-content">
                      <div className="entry-main">
                        <div className="entry-amount">
                          {formatCurrency(entry.amount, currency)}
                        </div>
                        <div className="entry-type">
                          {SAVINGS_TYPE_LABELS[entry.type]}
                        </div>
                      </div>
                      <div className="entry-details">
                        <div className="entry-date">
                          <FaCalendarAlt className="date-icon" />
                          {format(new Date(entry.date), 'MMM dd, yyyy • h:mm a')}
                        </div>
                        {entry.notes && (
                          <div className="entry-notes">"{entry.notes}"</div>
                        )}
                      </div>
                    </div>
                    <div className="entry-actions">
                      <button
                        onClick={() => onEditEntry(entry)}
                        className="action-btn edit-btn"
                        title="Edit entry"
                      >
                        <FaEdit />
                      </button>
                      <button
                        onClick={() => handleDelete(entry.id)}
                        className="action-btn delete-btn"
                        title="Delete entry"
                      >
                        <FaTrash />
                      </button>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        ))}
      </div>

      {sortedEntries.length === 0 && (searchTerm || filterType !== 'all') && (
        <div className="no-results">
          <p>No entries match your search criteria.</p>
        </div>
      )}
    </div>
  );
}

export default SavingsHistory;
