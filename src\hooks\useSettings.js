import { useState, useEffect, useCallback } from 'react';
import { settingsStorage } from '../utils/localStorage';

/**
 * Custom hook for managing user settings
 * @returns {Object} Settings data and methods
 */
export function useSettings() {
  const [settings, setSettings] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Load settings from storage on mount
  useEffect(() => {
    try {
      const savedSettings = settingsStorage.get();
      setSettings(savedSettings);
    } catch (err) {
      setError('Failed to load settings');
      console.error('Error loading settings:', err);
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * Updates a specific setting
   * @param {string} key - The setting key
   * @param {*} value - The setting value
   * @returns {Promise<Object>} Result with success status
   */
  const updateSetting = useCallback(async (key, value) => {
    try {
      const success = settingsStorage.update(key, value);
      if (success) {
        setSettings(prev => ({ ...prev, [key]: value }));
        return { success: true };
      } else {
        return {
          success: false,
          error: 'Failed to update setting'
        };
      }
    } catch (err) {
      console.error('Error updating setting:', err);
      return {
        success: false,
        error: 'An unexpected error occurred'
      };
    }
  }, []);

  /**
   * Updates multiple settings at once
   * @param {Object} newSettings - Object with settings to update
   * @returns {Promise<Object>} Result with success status
   */
  const updateSettings = useCallback(async (newSettings) => {
    try {
      const updatedSettings = { ...settings, ...newSettings };
      const success = settingsStorage.save(updatedSettings);
      if (success) {
        setSettings(updatedSettings);
        return { success: true };
      } else {
        return {
          success: false,
          error: 'Failed to update settings'
        };
      }
    } catch (err) {
      console.error('Error updating settings:', err);
      return {
        success: false,
        error: 'An unexpected error occurred'
      };
    }
  }, [settings]);

  /**
   * Resets settings to default values
   * @returns {Promise<Object>} Result with success status
   */
  const resetSettings = useCallback(async () => {
    try {
      const defaultSettings = {
        darkMode: false,
        currency: '₱',
        reminderEnabled: true,
        reminderFrequency: 'daily',
        reminderTime: '09:00',
        savingsGoal: 0,
        notifications: true
      };
      
      const success = settingsStorage.save(defaultSettings);
      if (success) {
        setSettings(defaultSettings);
        return { success: true };
      } else {
        return {
          success: false,
          error: 'Failed to reset settings'
        };
      }
    } catch (err) {
      console.error('Error resetting settings:', err);
      return {
        success: false,
        error: 'An unexpected error occurred'
      };
    }
  }, []);

  /**
   * Toggles dark mode
   * @returns {Promise<Object>} Result with success status
   */
  const toggleDarkMode = useCallback(async () => {
    return updateSetting('darkMode', !settings?.darkMode);
  }, [settings?.darkMode, updateSetting]);

  /**
   * Toggles notifications
   * @returns {Promise<Object>} Result with success status
   */
  const toggleNotifications = useCallback(async () => {
    return updateSetting('notifications', !settings?.notifications);
  }, [settings?.notifications, updateSetting]);

  /**
   * Toggles reminders
   * @returns {Promise<Object>} Result with success status
   */
  const toggleReminders = useCallback(async () => {
    return updateSetting('reminderEnabled', !settings?.reminderEnabled);
  }, [settings?.reminderEnabled, updateSetting]);

  return {
    // Data
    settings,
    loading,
    error,

    // Methods
    updateSetting,
    updateSettings,
    resetSettings,
    toggleDarkMode,
    toggleNotifications,
    toggleReminders,

    // Computed values
    isDarkMode: settings?.darkMode || false,
    currency: settings?.currency || '₱',
    reminderEnabled: settings?.reminderEnabled || false,
    reminderFrequency: settings?.reminderFrequency || 'daily',
    reminderTime: settings?.reminderTime || '09:00',
    savingsGoal: settings?.savingsGoal || 0,
    notificationsEnabled: settings?.notifications || false
  };
}
