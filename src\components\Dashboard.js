import React from 'react';
import { FaPiggyBank, FaCoins, FaCalendarAlt, FaTrophy } from 'react-icons/fa';
import { formatCurrency } from '../utils/dataModels';
import { format } from 'date-fns';
import './Dashboard.css';

/**
 * Dashboard component showing savings overview
 */
function Dashboard({ statistics, lastEntry, currency, savingsGoal }) {
  const goalProgress = savingsGoal > 0 ? (statistics.total / savingsGoal) * 100 : 0;
  const goalProgressCapped = Math.min(goalProgress, 100);

  return (
    <div className="dashboard">
      <div className="welcome-section">
        <h2>Welcome back, Saver! 🎯</h2>
        <p>Keep up the great work on your savings journey!</p>
      </div>

      {/* Stats Cards */}
      <div className="stats-grid">
        <div className="stat-card primary">
          <div className="stat-icon">
            <FaPiggyBank />
          </div>
          <div className="stat-content">
            <h3>Total Saved</h3>
            <p className="stat-value">{formatCurrency(statistics.total, currency)}</p>
          </div>
        </div>

        <div className="stat-card secondary">
          <div className="stat-icon">
            <FaCoins />
          </div>
          <div className="stat-content">
            <h3>Savings Count</h3>
            <p className="stat-value">{statistics.count}</p>
            <p className="stat-subtitle">entries</p>
          </div>
        </div>

        <div className="stat-card tertiary">
          <div className="stat-icon">
            <FaCalendarAlt />
          </div>
          <div className="stat-content">
            <h3>Average Saving</h3>
            <p className="stat-value">
              {statistics.count > 0 
                ? formatCurrency(statistics.average, currency)
                : formatCurrency(0, currency)
              }
            </p>
          </div>
        </div>

        {savingsGoal > 0 && (
          <div className="stat-card goal">
            <div className="stat-icon">
              <FaTrophy />
            </div>
            <div className="stat-content">
              <h3>Goal Progress</h3>
              <p className="stat-value">{goalProgressCapped.toFixed(1)}%</p>
              <div className="progress-bar">
                <div 
                  className="progress-fill" 
                  style={{ width: `${goalProgressCapped}%` }}
                ></div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Savings Goal Section */}
      {savingsGoal > 0 && (
        <div className="goal-section">
          <h3>Savings Goal</h3>
          <div className="goal-info">
            <div className="goal-amounts">
              <span className="current-amount">
                {formatCurrency(statistics.total, currency)}
              </span>
              <span className="goal-separator">of</span>
              <span className="goal-amount">
                {formatCurrency(savingsGoal, currency)}
              </span>
            </div>
            <div className="goal-progress-bar">
              <div 
                className="goal-progress-fill" 
                style={{ width: `${goalProgressCapped}%` }}
              ></div>
            </div>
            <div className="goal-remaining">
              {goalProgress >= 100 ? (
                <span className="goal-achieved">🎉 Goal Achieved!</span>
              ) : (
                <span>
                  {formatCurrency(savingsGoal - statistics.total, currency)} remaining
                </span>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Recent Activity */}
      {lastEntry && (
        <div className="recent-activity">
          <h3>Last Savings Entry</h3>
          <div className="activity-card">
            <div className="activity-amount">
              {formatCurrency(lastEntry.amount, currency)}
            </div>
            <div className="activity-details">
              <p className="activity-type">{lastEntry.type.toUpperCase()}</p>
              <p className="activity-date">
                {format(new Date(lastEntry.date), 'MMM dd, yyyy')}
              </p>
              {lastEntry.notes && (
                <p className="activity-notes">"{lastEntry.notes}"</p>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Motivational Message */}
      <div className="motivation-section">
        {statistics.count === 0 ? (
          <div className="motivation-card">
            <h3>Start Your Savings Journey! 🚀</h3>
            <p>Every peso saved is a step towards your financial goals. Add your first savings entry to get started!</p>
          </div>
        ) : (
          <div className="motivation-card">
            <h3>Keep Going! 💪</h3>
            <p>
              {statistics.count === 1 
                ? "Great start! You've made your first savings entry."
                : `Amazing! You've saved ${statistics.count} times.`
              } Every small amount counts towards your financial future!
            </p>
          </div>
        )}
      </div>
    </div>
  );
}

export default Dashboard;
