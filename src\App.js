import React, { useState } from 'react';
import Layout from './components/Layout';
import Dashboard from './components/Dashboard';
import SavingsForm from './components/SavingsForm';
import SavingsHistory from './components/SavingsHistory';
import Settings from './components/Settings';
import { useSavings } from './hooks/useSavings';
import { useSettings } from './hooks/useSettings';
import './App.css';

function App() {
  const [currentView, setCurrentView] = useState('dashboard');
  const [notification, setNotification] = useState(null);

  const {
    entries,
    statistics,
    lastEntry,
    addEntry,
    deleteEntry,
    clearAllEntries,
    loading: savingsLoading
  } = useSavings();

  const {
    settings,
    isDarkMode,
    currency,
    savingsGoal,
    updateSetting,
    updateSettings,
    resetSettings,
    loading: settingsLoading
  } = useSettings();

  // Show loading state while data is being loaded
  if (savingsLoading || settingsLoading) {
    return (
      <div className="loading-screen">
        <div className="loading-content">
          <h2>🐷 Loading Ipon Challenge...</h2>
          <div className="loading-spinner"></div>
        </div>
      </div>
    );
  }

  // Handle form submission
  const handleAddEntry = async (entryData) => {
    const result = await addEntry(entryData);

    if (result.success) {
      setNotification({
        type: 'success',
        message: `Successfully saved ${currency}${entryData.amount}! 🎉`
      });
      setCurrentView('dashboard');

      // Clear notification after 3 seconds
      setTimeout(() => setNotification(null), 3000);
    } else {
      setNotification({
        type: 'error',
        message: result.error || 'Failed to save entry'
      });

      // Clear notification after 5 seconds
      setTimeout(() => setNotification(null), 5000);
    }
  };

  // Handle entry deletion
  const handleDeleteEntry = async (entryId) => {
    const result = await deleteEntry(entryId);

    if (result.success) {
      setNotification({
        type: 'success',
        message: 'Entry deleted successfully!'
      });
      setTimeout(() => setNotification(null), 3000);
    } else {
      setNotification({
        type: 'error',
        message: result.error || 'Failed to delete entry'
      });
      setTimeout(() => setNotification(null), 5000);
    }
  };

  // Handle entry editing (placeholder for now)
  const handleEditEntry = (entry) => {
    // TODO: Implement edit functionality
    console.log('Edit entry:', entry);
    setNotification({
      type: 'success',
      message: 'Edit functionality coming soon!'
    });
    setTimeout(() => setNotification(null), 3000);
  };

  // Handle clear all data
  const handleClearAllData = async () => {
    const result = await clearAllEntries();

    if (result.success) {
      setNotification({
        type: 'success',
        message: 'All data cleared successfully!'
      });
      setTimeout(() => setNotification(null), 3000);
    } else {
      setNotification({
        type: 'error',
        message: result.error || 'Failed to clear data'
      });
      setTimeout(() => setNotification(null), 5000);
    }

    return result;
  };

  const renderCurrentView = () => {
    switch (currentView) {
      case 'dashboard':
        return (
          <Dashboard
            statistics={statistics}
            lastEntry={lastEntry}
            currency={currency}
            savingsGoal={savingsGoal}
          />
        );
      case 'add':
        return (
          <SavingsForm
            onSubmit={handleAddEntry}
            onCancel={() => setCurrentView('dashboard')}
            currency={currency}
          />
        );
      case 'history':
        return (
          <SavingsHistory
            entries={entries}
            statistics={statistics}
            currency={currency}
            onDeleteEntry={handleDeleteEntry}
            onEditEntry={handleEditEntry}
          />
        );
      case 'settings':
        return (
          <Settings
            settings={settings}
            onUpdateSetting={updateSetting}
            onUpdateSettings={updateSettings}
            onResetSettings={resetSettings}
            onClearAllData={handleClearAllData}
            totalSavings={statistics.total}
            entryCount={statistics.count}
          />
        );
      default:
        return (
          <Dashboard
            statistics={statistics}
            lastEntry={lastEntry}
            currency={currency}
            savingsGoal={savingsGoal}
          />
        );
    }
  };

  return (
    <Layout
      currentView={currentView}
      onViewChange={setCurrentView}
      darkMode={isDarkMode}
    >
      {/* Notification */}
      {notification && (
        <div className={`notification ${notification.type}`}>
          {notification.message}
        </div>
      )}

      {renderCurrentView()}
    </Layout>
  );
}

export default App;
