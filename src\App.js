import React, { useState } from 'react';
import Layout from './components/Layout';
import Dashboard from './components/Dashboard';
import SavingsForm from './components/SavingsForm';
import { useSavings } from './hooks/useSavings';
import { useSettings } from './hooks/useSettings';
import './App.css';

function App() {
  const [currentView, setCurrentView] = useState('dashboard');
  const [notification, setNotification] = useState(null);

  const {
    statistics,
    lastEntry,
    addEntry,
    loading: savingsLoading
  } = useSavings();

  const {
    isDarkMode,
    currency,
    savingsGoal,
    loading: settingsLoading
  } = useSettings();

  // Show loading state while data is being loaded
  if (savingsLoading || settingsLoading) {
    return (
      <div className="loading-screen">
        <div className="loading-content">
          <h2>🐷 Loading Ipon Challenge...</h2>
          <div className="loading-spinner"></div>
        </div>
      </div>
    );
  }

  // Handle form submission
  const handleAddEntry = async (entryData) => {
    const result = await addEntry(entryData);

    if (result.success) {
      setNotification({
        type: 'success',
        message: `Successfully saved ${currency}${entryData.amount}! 🎉`
      });
      setCurrentView('dashboard');

      // Clear notification after 3 seconds
      setTimeout(() => setNotification(null), 3000);
    } else {
      setNotification({
        type: 'error',
        message: result.error || 'Failed to save entry'
      });

      // Clear notification after 5 seconds
      setTimeout(() => setNotification(null), 5000);
    }
  };

  const renderCurrentView = () => {
    switch (currentView) {
      case 'dashboard':
        return (
          <Dashboard
            statistics={statistics}
            lastEntry={lastEntry}
            currency={currency}
            savingsGoal={savingsGoal}
          />
        );
      case 'add':
        return (
          <SavingsForm
            onSubmit={handleAddEntry}
            onCancel={() => setCurrentView('dashboard')}
            currency={currency}
          />
        );
      case 'history':
        return <div className="placeholder">Savings History - Coming Soon!</div>;
      case 'settings':
        return <div className="placeholder">Settings - Coming Soon!</div>;
      default:
        return (
          <Dashboard
            statistics={statistics}
            lastEntry={lastEntry}
            currency={currency}
            savingsGoal={savingsGoal}
          />
        );
    }
  };

  return (
    <Layout
      currentView={currentView}
      onViewChange={setCurrentView}
      darkMode={isDarkMode}
    >
      {/* Notification */}
      {notification && (
        <div className={`notification ${notification.type}`}>
          {notification.message}
        </div>
      )}

      {renderCurrentView()}
    </Layout>
  );
}

export default App;
