import React, { useState } from 'react';
import { 
  <PERSON>a<PERSON>oon, 
  <PERSON>a<PERSON>un, 
  FaBell, 
  FaBellSlash, 
  FaBullseye,
  FaDollarSign, 
  FaClock, 
  FaDownload, 
  FaUpload, 
  FaTrash,
  FaSave
} from 'react-icons/fa';
import { formatCurrency } from '../utils/dataModels';
import { exportData, importData } from '../utils/localStorage';
import './Settings.css';

/**
 * Settings component for app configuration
 */
function Settings({ 
  settings, 
  onUpdateSetting, 
  onUpdateSettings, 
  onResetSettings,
  onClearAllData,
  totalSavings,
  entryCount 
}) {
  const [goalInput, setGoalInput] = useState(settings?.savingsGoal || 0);
  const [isExporting, setIsExporting] = useState(false);
  const [notification, setNotification] = useState(null);

  const showNotification = (message, type = 'success') => {
    setNotification({ message, type });
    setTimeout(() => setNotification(null), 3000);
  };

  const handleGoalSave = async () => {
    const result = await onUpdateSetting('savingsGoal', parseFloat(goalInput) || 0);
    if (result.success) {
      showNotification('Savings goal updated successfully!');
    } else {
      showNotification('Failed to update savings goal', 'error');
    }
  };

  const handleExportData = async () => {
    setIsExporting(true);
    try {
      const data = exportData();
      const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `ipon-challenge-backup-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
      showNotification('Data exported successfully!');
    } catch (error) {
      showNotification('Failed to export data', 'error');
    } finally {
      setIsExporting(false);
    }
  };

  const handleImportData = async (event) => {
    const file = event.target.files[0];
    if (!file) return;

    try {
      const text = await file.text();
      const data = JSON.parse(text);
      const success = importData(data);
      
      if (success) {
        showNotification('Data imported successfully! Please refresh the page.');
      } else {
        showNotification('Failed to import data', 'error');
      }
    } catch (error) {
      showNotification('Invalid backup file', 'error');
    }
    
    // Reset file input
    event.target.value = '';
  };

  const handleClearData = async () => {
    const confirmed = window.confirm(
      'Are you sure you want to clear all savings data? This action cannot be undone.'
    );
    
    if (confirmed) {
      const result = await onClearAllData();
      if (result.success) {
        showNotification('All data cleared successfully!');
      } else {
        showNotification('Failed to clear data', 'error');
      }
    }
  };

  const handleResetSettings = async () => {
    const confirmed = window.confirm(
      'Are you sure you want to reset all settings to default values?'
    );
    
    if (confirmed) {
      const result = await onResetSettings();
      if (result.success) {
        setGoalInput(0);
        showNotification('Settings reset successfully!');
      } else {
        showNotification('Failed to reset settings', 'error');
      }
    }
  };

  if (!settings) {
    return <div className="settings-loading">Loading settings...</div>;
  }

  return (
    <div className="settings">
      {/* Notification */}
      {notification && (
        <div className={`settings-notification ${notification.type}`}>
          {notification.message}
        </div>
      )}

      <div className="settings-header">
        <h2>Settings</h2>
        <p>Customize your Ipon Challenge experience</p>
      </div>

      <div className="settings-sections">
        {/* Appearance */}
        <div className="settings-section">
          <h3>Appearance</h3>
          <div className="setting-item">
            <div className="setting-info">
              <div className="setting-icon">
                {settings.darkMode ? <FaMoon /> : <FaSun />}
              </div>
              <div className="setting-details">
                <div className="setting-title">Dark Mode</div>
                <div className="setting-description">
                  Switch between light and dark themes
                </div>
              </div>
            </div>
            <label className="toggle-switch">
              <input
                type="checkbox"
                checked={settings.darkMode}
                onChange={(e) => onUpdateSetting('darkMode', e.target.checked)}
              />
              <span className="toggle-slider"></span>
            </label>
          </div>
        </div>

        {/* Currency */}
        <div className="settings-section">
          <h3>Currency</h3>
          <div className="setting-item">
            <div className="setting-info">
              <div className="setting-icon">
                <FaDollarSign />
              </div>
              <div className="setting-details">
                <div className="setting-title">Currency Symbol</div>
                <div className="setting-description">
                  Choose your preferred currency symbol
                </div>
              </div>
            </div>
            <select
              value={settings.currency}
              onChange={(e) => onUpdateSetting('currency', e.target.value)}
              className="currency-select"
            >
              <option value="₱">₱ (Philippine Peso)</option>
              <option value="$">$ (US Dollar)</option>
              <option value="€">€ (Euro)</option>
              <option value="£">£ (British Pound)</option>
              <option value="¥">¥ (Japanese Yen)</option>
              <option value="₹">₹ (Indian Rupee)</option>
            </select>
          </div>
        </div>

        {/* Savings Goal */}
        <div className="settings-section">
          <h3>Savings Goal</h3>
          <div className="setting-item">
            <div className="setting-info">
              <div className="setting-icon">
                <FaBullseye />
              </div>
              <div className="setting-details">
                <div className="setting-title">Target Amount</div>
                <div className="setting-description">
                  Set a savings goal to track your progress
                </div>
              </div>
            </div>
            <div className="goal-input-group">
              <input
                type="number"
                value={goalInput}
                onChange={(e) => setGoalInput(e.target.value)}
                placeholder="0.00"
                step="0.01"
                min="0"
                className="goal-input"
              />
              <button onClick={handleGoalSave} className="goal-save-btn">
                <FaSave />
              </button>
            </div>
          </div>
        </div>

        {/* Notifications */}
        <div className="settings-section">
          <h3>Notifications</h3>
          <div className="setting-item">
            <div className="setting-info">
              <div className="setting-icon">
                {settings.notifications ? <FaBell /> : <FaBellSlash />}
              </div>
              <div className="setting-details">
                <div className="setting-title">Enable Notifications</div>
                <div className="setting-description">
                  Receive browser notifications for reminders
                </div>
              </div>
            </div>
            <label className="toggle-switch">
              <input
                type="checkbox"
                checked={settings.notifications}
                onChange={(e) => onUpdateSetting('notifications', e.target.checked)}
              />
              <span className="toggle-slider"></span>
            </label>
          </div>

          <div className="setting-item">
            <div className="setting-info">
              <div className="setting-icon">
                <FaClock />
              </div>
              <div className="setting-details">
                <div className="setting-title">Reminder Frequency</div>
                <div className="setting-description">
                  How often to remind you to save
                </div>
              </div>
            </div>
            <select
              value={settings.reminderFrequency}
              onChange={(e) => onUpdateSetting('reminderFrequency', e.target.value)}
              className="frequency-select"
              disabled={!settings.notifications}
            >
              <option value="daily">Daily</option>
              <option value="weekly">Weekly</option>
              <option value="monthly">Monthly</option>
            </select>
          </div>
        </div>

        {/* Data Management */}
        <div className="settings-section">
          <h3>Data Management</h3>
          
          {/* Stats Display */}
          <div className="data-stats">
            <div className="stat-item">
              <strong>{entryCount}</strong> savings entries
            </div>
            <div className="stat-item">
              <strong>{formatCurrency(totalSavings, settings.currency)}</strong> total saved
            </div>
          </div>

          {/* Export Data */}
          <div className="setting-item">
            <div className="setting-info">
              <div className="setting-icon">
                <FaDownload />
              </div>
              <div className="setting-details">
                <div className="setting-title">Export Data</div>
                <div className="setting-description">
                  Download a backup of all your savings data
                </div>
              </div>
            </div>
            <button 
              onClick={handleExportData} 
              className="btn btn-secondary"
              disabled={isExporting}
            >
              {isExporting ? 'Exporting...' : 'Export'}
            </button>
          </div>

          {/* Import Data */}
          <div className="setting-item">
            <div className="setting-info">
              <div className="setting-icon">
                <FaUpload />
              </div>
              <div className="setting-details">
                <div className="setting-title">Import Data</div>
                <div className="setting-description">
                  Restore data from a backup file
                </div>
              </div>
            </div>
            <label className="btn btn-secondary file-input-label">
              Import
              <input
                type="file"
                accept=".json"
                onChange={handleImportData}
                className="file-input"
              />
            </label>
          </div>

          {/* Clear Data */}
          <div className="setting-item danger">
            <div className="setting-info">
              <div className="setting-icon">
                <FaTrash />
              </div>
              <div className="setting-details">
                <div className="setting-title">Clear All Data</div>
                <div className="setting-description">
                  Permanently delete all savings entries
                </div>
              </div>
            </div>
            <button onClick={handleClearData} className="btn btn-danger">
              Clear Data
            </button>
          </div>
        </div>

        {/* Reset Settings */}
        <div className="settings-section">
          <div className="setting-item danger">
            <div className="setting-info">
              <div className="setting-details">
                <div className="setting-title">Reset Settings</div>
                <div className="setting-description">
                  Reset all settings to default values
                </div>
              </div>
            </div>
            <button onClick={handleResetSettings} className="btn btn-danger">
              Reset Settings
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}

export default Settings;
