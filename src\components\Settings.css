/* Settings Component Styles */
.settings {
  max-width: 700px;
  margin: 0 auto;
}

.settings-loading {
  text-align: center;
  padding: 3rem;
  color: #4a5568;
  font-size: 1.1rem;
}

/* Notification */
.settings-notification {
  position: fixed;
  top: 1rem;
  right: 1rem;
  padding: 1rem 1.5rem;
  border-radius: 8px;
  color: white;
  font-weight: 600;
  z-index: 1000;
  animation: slideIn 0.3s ease;
  max-width: 300px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
}

.settings-notification.success {
  background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
}

.settings-notification.error {
  background: linear-gradient(135deg, #e53e3e 0%, #c53030 100%);
}

/* Header */
.settings-header {
  background: rgba(255, 255, 255, 0.9);
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  margin-bottom: 2rem;
  text-align: center;
}

.settings-header h2 {
  margin: 0 0 0.5rem 0;
  color: #2d3748;
  font-size: 1.75rem;
}

.settings-header p {
  margin: 0;
  color: #4a5568;
  font-size: 1rem;
}

/* Settings Sections */
.settings-sections {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.settings-section {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.settings-section h3 {
  background: #f7fafc;
  margin: 0;
  padding: 1.5rem;
  color: #2d3748;
  font-size: 1.1rem;
  font-weight: 600;
  border-bottom: 1px solid #e2e8f0;
}

/* Setting Items */
.setting-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.5rem;
  border-bottom: 1px solid #f1f5f9;
}

.setting-item:last-child {
  border-bottom: none;
}

.setting-item.danger {
  background: rgba(254, 215, 215, 0.3);
}

.setting-info {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex: 1;
}

.setting-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1rem;
  flex-shrink: 0;
}

.setting-details {
  flex: 1;
}

.setting-title {
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 0.25rem;
}

.setting-description {
  font-size: 0.9rem;
  color: #718096;
}

/* Toggle Switch */
.toggle-switch {
  position: relative;
  display: inline-block;
  width: 50px;
  height: 24px;
}

.toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #cbd5e0;
  transition: 0.3s;
  border-radius: 24px;
}

.toggle-slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 3px;
  background-color: white;
  transition: 0.3s;
  border-radius: 50%;
}

input:checked + .toggle-slider {
  background-color: #4299e1;
}

input:checked + .toggle-slider:before {
  transform: translateX(26px);
}

/* Select Inputs */
.currency-select,
.frequency-select {
  padding: 0.5rem 0.75rem;
  border: 2px solid #e2e8f0;
  border-radius: 6px;
  background: white;
  color: #2d3748;
  font-size: 0.9rem;
  cursor: pointer;
  transition: border-color 0.2s ease;
}

.currency-select:focus,
.frequency-select:focus {
  outline: none;
  border-color: #4299e1;
}

.frequency-select:disabled {
  background: #f7fafc;
  color: #a0aec0;
  cursor: not-allowed;
}

/* Goal Input */
.goal-input-group {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.goal-input {
  padding: 0.5rem 0.75rem;
  border: 2px solid #e2e8f0;
  border-radius: 6px;
  font-size: 0.9rem;
  width: 120px;
  transition: border-color 0.2s ease;
}

.goal-input:focus {
  outline: none;
  border-color: #4299e1;
}

.goal-save-btn {
  padding: 0.5rem;
  background: #4299e1;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: background-color 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
}

.goal-save-btn:hover {
  background: #3182ce;
}

/* Data Stats */
.data-stats {
  display: flex;
  gap: 2rem;
  padding: 1rem 1.5rem;
  background: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
}

.stat-item {
  color: #4a5568;
  font-size: 0.9rem;
}

.stat-item strong {
  color: #2d3748;
  font-size: 1rem;
}

/* File Input */
.file-input-label {
  position: relative;
  cursor: pointer;
}

.file-input {
  position: absolute;
  opacity: 0;
  width: 0;
  height: 0;
}

/* Button Styles */
.btn {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 6px;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  display: inline-block;
}

.btn-secondary {
  background: #e2e8f0;
  color: #4a5568;
}

.btn-secondary:hover {
  background: #cbd5e0;
}

.btn-danger {
  background: #e53e3e;
  color: white;
}

.btn-danger:hover {
  background: #c53030;
}

/* Responsive Design */
@media (max-width: 768px) {
  .settings {
    margin: 0 -1rem;
  }
  
  .settings-header {
    margin-left: 1rem;
    margin-right: 1rem;
    padding: 1.5rem;
  }
  
  .settings-sections {
    margin: 0 1rem;
  }
  
  .setting-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }
  
  .setting-info {
    width: 100%;
  }
  
  .data-stats {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .goal-input-group {
    width: 100%;
    justify-content: flex-end;
  }
}

@media (max-width: 480px) {
  .settings-header h2 {
    font-size: 1.5rem;
  }
  
  .setting-item {
    padding: 1rem;
  }
  
  .setting-icon {
    width: 36px;
    height: 36px;
    font-size: 0.9rem;
  }
  
  .settings-notification {
    top: 0.5rem;
    right: 0.5rem;
    left: 0.5rem;
    max-width: none;
  }
}
