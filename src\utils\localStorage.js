/**
 * Local storage utilities for the Ipon Challenge app
 */

// Storage keys
const STORAGE_KEYS = {
  SAVINGS_ENTRIES: 'ipon_challenge_savings',
  USER_SETTINGS: 'ipon_challenge_settings',
  REMINDERS: 'ipon_challenge_reminders'
};

/**
 * Safely gets data from localStorage
 * @param {string} key - The storage key
 * @param {*} defaultValue - Default value if key doesn't exist
 * @returns {*} The stored data or default value
 */
function getFromStorage(key, defaultValue = null) {
  try {
    const item = localStorage.getItem(key);
    return item ? JSON.parse(item) : defaultValue;
  } catch (error) {
    console.error(`Error reading from localStorage (${key}):`, error);
    return defaultValue;
  }
}

/**
 * Safely sets data to localStorage
 * @param {string} key - The storage key
 * @param {*} value - The value to store
 * @returns {boolean} Success status
 */
function setToStorage(key, value) {
  try {
    localStorage.setItem(key, JSON.stringify(value));
    return true;
  } catch (error) {
    console.error(`<PERSON><PERSON>r writing to localStorage (${key}):`, error);
    return false;
  }
}

/**
 * Removes an item from localStorage
 * @param {string} key - The storage key
 * @returns {boolean} Success status
 */
function removeFromStorage(key) {
  try {
    localStorage.removeItem(key);
    return true;
  } catch (error) {
    console.error(`Error removing from localStorage (${key}):`, error);
    return false;
  }
}

// Savings entries storage functions
export const savingsStorage = {
  /**
   * Gets all savings entries from storage
   * @returns {Array} Array of savings entries
   */
  getAll() {
    const entries = getFromStorage(STORAGE_KEYS.SAVINGS_ENTRIES, []);
    // Convert date strings back to Date objects
    return entries.map(entry => ({
      ...entry,
      date: new Date(entry.date),
      createdAt: new Date(entry.createdAt)
    }));
  },

  /**
   * Saves all savings entries to storage
   * @param {Array} entries - Array of savings entries
   * @returns {boolean} Success status
   */
  saveAll(entries) {
    return setToStorage(STORAGE_KEYS.SAVINGS_ENTRIES, entries);
  },

  /**
   * Adds a new savings entry
   * @param {Object} entry - The savings entry to add
   * @returns {boolean} Success status
   */
  add(entry) {
    const entries = this.getAll();
    entries.push(entry);
    return this.saveAll(entries);
  },

  /**
   * Updates an existing savings entry
   * @param {string} id - The entry ID
   * @param {Object} updatedEntry - The updated entry data
   * @returns {boolean} Success status
   */
  update(id, updatedEntry) {
    const entries = this.getAll();
    const index = entries.findIndex(entry => entry.id === id);
    
    if (index === -1) {
      console.error(`Entry with ID ${id} not found`);
      return false;
    }

    entries[index] = { ...entries[index], ...updatedEntry };
    return this.saveAll(entries);
  },

  /**
   * Deletes a savings entry
   * @param {string} id - The entry ID to delete
   * @returns {boolean} Success status
   */
  delete(id) {
    const entries = this.getAll();
    const filteredEntries = entries.filter(entry => entry.id !== id);
    
    if (filteredEntries.length === entries.length) {
      console.error(`Entry with ID ${id} not found`);
      return false;
    }

    return this.saveAll(filteredEntries);
  },

  /**
   * Gets a single savings entry by ID
   * @param {string} id - The entry ID
   * @returns {Object|null} The savings entry or null if not found
   */
  getById(id) {
    const entries = this.getAll();
    return entries.find(entry => entry.id === id) || null;
  },

  /**
   * Clears all savings entries
   * @returns {boolean} Success status
   */
  clear() {
    return removeFromStorage(STORAGE_KEYS.SAVINGS_ENTRIES);
  }
};

// User settings storage functions
export const settingsStorage = {
  /**
   * Gets user settings from storage
   * @returns {Object} User settings object
   */
  get() {
    return getFromStorage(STORAGE_KEYS.USER_SETTINGS, {
      darkMode: false,
      currency: '₱',
      reminderEnabled: true,
      reminderFrequency: 'daily', // daily, weekly, monthly
      reminderTime: '09:00',
      savingsGoal: 0,
      notifications: true
    });
  },

  /**
   * Saves user settings to storage
   * @param {Object} settings - The settings object
   * @returns {boolean} Success status
   */
  save(settings) {
    return setToStorage(STORAGE_KEYS.USER_SETTINGS, settings);
  },

  /**
   * Updates specific setting
   * @param {string} key - The setting key
   * @param {*} value - The setting value
   * @returns {boolean} Success status
   */
  update(key, value) {
    const settings = this.get();
    settings[key] = value;
    return this.save(settings);
  }
};

// Reminders storage functions
export const remindersStorage = {
  /**
   * Gets reminder data from storage
   * @returns {Object} Reminder data
   */
  get() {
    return getFromStorage(STORAGE_KEYS.REMINDERS, {
      lastReminder: null,
      nextReminder: null,
      reminderCount: 0
    });
  },

  /**
   * Saves reminder data to storage
   * @param {Object} reminderData - The reminder data
   * @returns {boolean} Success status
   */
  save(reminderData) {
    return setToStorage(STORAGE_KEYS.REMINDERS, reminderData);
  }
};

/**
 * Exports all data for backup
 * @returns {Object} All app data
 */
export function exportData() {
  return {
    savings: savingsStorage.getAll(),
    settings: settingsStorage.get(),
    reminders: remindersStorage.get(),
    exportDate: new Date().toISOString()
  };
}

/**
 * Imports data from backup
 * @param {Object} data - The data to import
 * @returns {boolean} Success status
 */
export function importData(data) {
  try {
    if (data.savings) {
      savingsStorage.saveAll(data.savings);
    }
    if (data.settings) {
      settingsStorage.save(data.settings);
    }
    if (data.reminders) {
      remindersStorage.save(data.reminders);
    }
    return true;
  } catch (error) {
    console.error('Error importing data:', error);
    return false;
  }
}
