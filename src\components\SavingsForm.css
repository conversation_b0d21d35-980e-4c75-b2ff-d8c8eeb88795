/* Savings Form Component Styles */
.savings-form-container {
  max-width: 600px;
  margin: 0 auto;
}

.form-header {
  background: rgba(255, 255, 255, 0.9);
  padding: 2rem;
  border-radius: 12px 12px 0 0;
  text-align: center;
  margin-bottom: 0;
}

.form-header h2 {
  margin: 0 0 0.5rem 0;
  color: #2d3748;
  font-size: 1.75rem;
}

.form-header p {
  margin: 0;
  color: #4a5568;
  font-size: 1rem;
}

.savings-form {
  background: rgba(255, 255, 255, 0.9);
  padding: 2rem;
  border-radius: 0 0 12px 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

/* Form Groups */
.form-group {
  margin-bottom: 1.5rem;
}

.form-label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: #2d3748;
  font-size: 0.9rem;
}

.form-input {
  width: 100%;
  padding: 0.75rem;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.2s ease;
  background: white;
}

.form-input:focus {
  outline: none;
  border-color: #4299e1;
  box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.1);
}

.form-input.error {
  border-color: #e53e3e;
}

.form-input:disabled {
  background: #f7fafc;
  cursor: not-allowed;
}

/* Amount Input */
.amount-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.currency-symbol {
  position: absolute;
  left: 0.75rem;
  color: #4a5568;
  font-weight: 600;
  z-index: 1;
}

.amount-input {
  padding-left: 2.5rem;
  font-size: 1.25rem;
  font-weight: 600;
}

/* Type Options */
.type-options {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
  gap: 1rem;
}

.type-option {
  cursor: pointer;
}

.type-option input[type="radio"] {
  display: none;
}

.type-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  background: white;
  transition: all 0.2s ease;
  text-align: center;
}

.type-card:hover {
  border-color: #4299e1;
  background: #f7fafc;
}

.type-card.selected {
  border-color: #4299e1;
  background: #ebf8ff;
  color: #2b6cb0;
}

.type-icon {
  font-size: 1.5rem;
  color: #4a5568;
}

.type-card.selected .type-icon {
  color: #2b6cb0;
}

.type-label {
  font-size: 0.85rem;
  font-weight: 600;
}

/* Form Row */
.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

/* Textarea */
.form-textarea {
  resize: vertical;
  min-height: 80px;
}

/* Error Messages */
.error-message {
  display: block;
  margin-top: 0.25rem;
  font-size: 0.8rem;
  color: #e53e3e;
  font-weight: 500;
}

/* Form Actions */
.form-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  margin-top: 2rem;
  padding-top: 1.5rem;
  border-top: 1px solid #e2e8f0;
}

.form-actions .btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  min-width: 120px;
  justify-content: center;
}

/* Responsive Design */
@media (max-width: 768px) {
  .savings-form-container {
    margin: 0 -1rem;
  }
  
  .form-header,
  .savings-form {
    padding: 1.5rem;
  }
  
  .type-options {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .form-row {
    grid-template-columns: 1fr;
  }
  
  .form-actions {
    flex-direction: column-reverse;
  }
  
  .form-actions .btn {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .form-header h2 {
    font-size: 1.5rem;
  }
  
  .type-options {
    grid-template-columns: 1fr;
  }
  
  .type-card {
    flex-direction: row;
    justify-content: flex-start;
    text-align: left;
  }
}
