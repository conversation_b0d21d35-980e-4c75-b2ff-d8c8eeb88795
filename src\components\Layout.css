/* Layout Component Styles */
.layout {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  transition: all 0.3s ease;
}

.layout.dark-mode {
  background: linear-gradient(135deg, #2d3748 0%, #1a202c 100%);
}

/* Header Styles */
.layout-header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
  padding: 1rem 0;
  position: sticky;
  top: 0;
  z-index: 100;
}

.layout.dark-mode .layout-header {
  background: rgba(45, 55, 72, 0.95);
  color: white;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
  text-align: center;
}

.logo {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  margin-bottom: 0.5rem;
}

.logo-icon {
  font-size: 2rem;
  color: #4299e1;
}

.layout.dark-mode .logo-icon {
  color: #63b3ed;
}

.logo h1 {
  margin: 0;
  font-size: 2rem;
  font-weight: bold;
  color: #2d3748;
}

.layout.dark-mode .logo h1 {
  color: white;
}

.tagline {
  margin: 0;
  font-size: 1rem;
  color: #718096;
  font-style: italic;
}

.layout.dark-mode .tagline {
  color: #a0aec0;
}

/* Main Content */
.layout-main {
  flex: 1;
  padding: 2rem 1rem 6rem; /* Bottom padding for nav */
  overflow-y: auto;
}

.main-content {
  max-width: 1200px;
  margin: 0 auto;
}

/* Bottom Navigation */
.bottom-nav {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: space-around;
  padding: 0.75rem 0;
  z-index: 100;
}

.layout.dark-mode .bottom-nav {
  background: rgba(45, 55, 72, 0.95);
  border-top-color: rgba(255, 255, 255, 0.1);
}

.nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.25rem;
  background: none;
  border: none;
  color: #718096;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 8px;
  transition: all 0.2s ease;
  min-width: 60px;
}

.nav-item:hover {
  background: rgba(66, 153, 225, 0.1);
  color: #4299e1;
}

.nav-item.active {
  color: #4299e1;
  background: rgba(66, 153, 225, 0.1);
}

.layout.dark-mode .nav-item {
  color: #a0aec0;
}

.layout.dark-mode .nav-item:hover,
.layout.dark-mode .nav-item.active {
  color: #63b3ed;
  background: rgba(99, 179, 237, 0.1);
}

.nav-icon {
  font-size: 1.25rem;
}

.nav-label {
  font-size: 0.75rem;
  font-weight: 500;
}

/* Responsive Design */
@media (max-width: 768px) {
  .layout-header {
    padding: 0.75rem 0;
  }
  
  .logo h1 {
    font-size: 1.75rem;
  }
  
  .tagline {
    font-size: 0.9rem;
  }
  
  .layout-main {
    padding: 1.5rem 1rem 6rem;
  }
  
  .nav-label {
    font-size: 0.7rem;
  }
}

@media (max-width: 480px) {
  .logo {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .logo h1 {
    font-size: 1.5rem;
  }
  
  .layout-main {
    padding: 1rem 0.75rem 6rem;
  }
  
  .bottom-nav {
    padding: 0.5rem 0;
  }
  
  .nav-item {
    min-width: 50px;
    padding: 0.25rem;
  }
  
  .nav-icon {
    font-size: 1.1rem;
  }
  
  .nav-label {
    font-size: 0.65rem;
  }
}
