import React, { useState } from 'react';
import { FaSave, FaTimes, FaPiggyBank, FaUniversity, FaCreditCard, FaMobile } from 'react-icons/fa';
import { SAVINGS_TYPES, SAVINGS_TYPE_LABELS } from '../utils/dataModels';
import { format } from 'date-fns';
import './SavingsForm.css';

/**
 * Form component for adding new savings entries
 */
function SavingsForm({ onSubmit, onCancel, currency = '₱' }) {
  const [formData, setFormData] = useState({
    amount: '',
    type: '',
    notes: '',
    date: format(new Date(), 'yyyy-MM-dd'),
    time: format(new Date(), 'HH:mm')
  });
  const [errors, setErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  const typeIcons = {
    [SAVINGS_TYPES.BOX]: FaPiggyBank,
    [SAVINGS_TYPES.ATM]: FaCreditCard,
    [SAVINGS_TYPES.BANK]: FaUniversity,
    [SAVINGS_TYPES.DIGITAL]: FaMobile
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.amount || parseFloat(formData.amount) <= 0) {
      newErrors.amount = 'Please enter a valid amount greater than 0';
    }

    if (!formData.type) {
      newErrors.type = 'Please select a savings type';
    }

    if (!formData.date) {
      newErrors.date = 'Please select a date';
    }

    if (!formData.time) {
      newErrors.time = 'Please select a time';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      // Combine date and time
      const dateTime = new Date(`${formData.date}T${formData.time}`);
      
      const entryData = {
        amount: parseFloat(formData.amount),
        type: formData.type,
        notes: formData.notes.trim(),
        date: dateTime
      };

      await onSubmit(entryData);
      
      // Reset form on successful submission
      setFormData({
        amount: '',
        type: '',
        notes: '',
        date: format(new Date(), 'yyyy-MM-dd'),
        time: format(new Date(), 'HH:mm')
      });
    } catch (error) {
      console.error('Error submitting form:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="savings-form-container">
      <div className="form-header">
        <h2>Add New Savings Entry</h2>
        <p>Record your latest savings to track your progress!</p>
      </div>

      <form onSubmit={handleSubmit} className="savings-form">
        {/* Amount Input */}
        <div className="form-group">
          <label htmlFor="amount" className="form-label">
            Amount Saved *
          </label>
          <div className="amount-input-wrapper">
            <span className="currency-symbol">{currency}</span>
            <input
              type="number"
              id="amount"
              name="amount"
              value={formData.amount}
              onChange={handleInputChange}
              placeholder="0.00"
              step="0.01"
              min="0"
              className={`form-input amount-input ${errors.amount ? 'error' : ''}`}
              disabled={isSubmitting}
            />
          </div>
          {errors.amount && <span className="error-message">{errors.amount}</span>}
        </div>

        {/* Savings Type */}
        <div className="form-group">
          <label className="form-label">Savings Type *</label>
          <div className="type-options">
            {Object.entries(SAVINGS_TYPES).map(([key, value]) => {
              const Icon = typeIcons[value];
              return (
                <label key={value} className="type-option">
                  <input
                    type="radio"
                    name="type"
                    value={value}
                    checked={formData.type === value}
                    onChange={handleInputChange}
                    disabled={isSubmitting}
                  />
                  <div className={`type-card ${formData.type === value ? 'selected' : ''}`}>
                    <Icon className="type-icon" />
                    <span className="type-label">{SAVINGS_TYPE_LABELS[value]}</span>
                  </div>
                </label>
              );
            })}
          </div>
          {errors.type && <span className="error-message">{errors.type}</span>}
        </div>

        {/* Date and Time */}
        <div className="form-row">
          <div className="form-group">
            <label htmlFor="date" className="form-label">Date *</label>
            <input
              type="date"
              id="date"
              name="date"
              value={formData.date}
              onChange={handleInputChange}
              className={`form-input ${errors.date ? 'error' : ''}`}
              disabled={isSubmitting}
            />
            {errors.date && <span className="error-message">{errors.date}</span>}
          </div>

          <div className="form-group">
            <label htmlFor="time" className="form-label">Time *</label>
            <input
              type="time"
              id="time"
              name="time"
              value={formData.time}
              onChange={handleInputChange}
              className={`form-input ${errors.time ? 'error' : ''}`}
              disabled={isSubmitting}
            />
            {errors.time && <span className="error-message">{errors.time}</span>}
          </div>
        </div>

        {/* Notes */}
        <div className="form-group">
          <label htmlFor="notes" className="form-label">
            Notes (Optional)
          </label>
          <textarea
            id="notes"
            name="notes"
            value={formData.notes}
            onChange={handleInputChange}
            placeholder="Add any notes about this savings entry..."
            rows="3"
            className="form-input form-textarea"
            disabled={isSubmitting}
          />
        </div>

        {/* Form Actions */}
        <div className="form-actions">
          <button
            type="button"
            onClick={onCancel}
            className="btn btn-secondary"
            disabled={isSubmitting}
          >
            <FaTimes /> Cancel
          </button>
          <button
            type="submit"
            className="btn btn-primary"
            disabled={isSubmitting}
          >
            <FaSave /> {isSubmitting ? 'Saving...' : 'Save Entry'}
          </button>
        </div>
      </form>
    </div>
  );
}

export default SavingsForm;
