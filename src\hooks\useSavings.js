import { useState, useEffect, useCallback } from 'react';
import { savingsStorage } from '../utils/localStorage';
import { createSavingsEntry, validateSavingsEntry, getSavingsStatistics } from '../utils/dataModels';

/**
 * Custom hook for managing savings data
 * @returns {Object} Savings data and methods
 */
export function useSavings() {
  const [entries, setEntries] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Load savings entries from storage on mount
  useEffect(() => {
    try {
      const savedEntries = savingsStorage.getAll();
      setEntries(savedEntries);
    } catch (err) {
      setError('Failed to load savings data');
      console.error('Error loading savings:', err);
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * Adds a new savings entry
   * @param {Object} entryData - The entry data
   * @returns {Promise<Object>} Result with success status and entry/error
   */
  const addEntry = useCallback(async (entryData) => {
    try {
      const newEntry = createSavingsEntry(entryData);
      const validation = validateSavingsEntry(newEntry);

      if (!validation.isValid) {
        return {
          success: false,
          error: validation.errors.join(', ')
        };
      }

      const success = savingsStorage.add(newEntry);
      if (success) {
        setEntries(prev => [...prev, newEntry]);
        return {
          success: true,
          entry: newEntry
        };
      } else {
        return {
          success: false,
          error: 'Failed to save entry'
        };
      }
    } catch (err) {
      console.error('Error adding entry:', err);
      return {
        success: false,
        error: 'An unexpected error occurred'
      };
    }
  }, []);

  /**
   * Updates an existing savings entry
   * @param {string} id - The entry ID
   * @param {Object} updatedData - The updated data
   * @returns {Promise<Object>} Result with success status
   */
  const updateEntry = useCallback(async (id, updatedData) => {
    try {
      const success = savingsStorage.update(id, updatedData);
      if (success) {
        setEntries(prev => 
          prev.map(entry => 
            entry.id === id ? { ...entry, ...updatedData } : entry
          )
        );
        return { success: true };
      } else {
        return {
          success: false,
          error: 'Failed to update entry'
        };
      }
    } catch (err) {
      console.error('Error updating entry:', err);
      return {
        success: false,
        error: 'An unexpected error occurred'
      };
    }
  }, []);

  /**
   * Deletes a savings entry
   * @param {string} id - The entry ID
   * @returns {Promise<Object>} Result with success status
   */
  const deleteEntry = useCallback(async (id) => {
    try {
      const success = savingsStorage.delete(id);
      if (success) {
        setEntries(prev => prev.filter(entry => entry.id !== id));
        return { success: true };
      } else {
        return {
          success: false,
          error: 'Failed to delete entry'
        };
      }
    } catch (err) {
      console.error('Error deleting entry:', err);
      return {
        success: false,
        error: 'An unexpected error occurred'
      };
    }
  }, []);

  /**
   * Clears all savings entries
   * @returns {Promise<Object>} Result with success status
   */
  const clearAllEntries = useCallback(async () => {
    try {
      const success = savingsStorage.clear();
      if (success) {
        setEntries([]);
        return { success: true };
      } else {
        return {
          success: false,
          error: 'Failed to clear entries'
        };
      }
    } catch (err) {
      console.error('Error clearing entries:', err);
      return {
        success: false,
        error: 'An unexpected error occurred'
      };
    }
  }, []);

  /**
   * Gets a specific entry by ID
   * @param {string} id - The entry ID
   * @returns {Object|null} The entry or null if not found
   */
  const getEntry = useCallback((id) => {
    return entries.find(entry => entry.id === id) || null;
  }, [entries]);

  // Calculate statistics
  const statistics = getSavingsStatistics(entries);

  // Sort entries by date (newest first)
  const sortedEntries = [...entries].sort((a, b) => new Date(b.date) - new Date(a.date));

  return {
    // Data
    entries: sortedEntries,
    statistics,
    loading,
    error,

    // Methods
    addEntry,
    updateEntry,
    deleteEntry,
    clearAllEntries,
    getEntry,

    // Computed values
    totalSavings: statistics.total,
    entryCount: statistics.count,
    averageSaving: statistics.average,
    lastEntry: statistics.lastEntry
  };
}
